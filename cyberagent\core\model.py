"""
Model interface for communicating with Ollama Qwen3:32b
"""

import asyncio
import json
import os
from typing import Dict, List, Optional, AsyncGenerator
import httpx
from pydantic import BaseModel
from .logging_config import get_logger  # Restore for non-task specific internal logs
from .task_logger import TaskLogger, get_task_logger  # Keep for actual task logs


class Message(BaseModel):
    role: str
    content: str


class ModelResponse(BaseModel):
    content: str
    done: bool
    model: str
    created_at: str
    response: str = ""


class OllamaClient:
    """Client for communicating with Ollama API"""

    def __init__(
        self,
        base_url: str = None,
        model: str = None,
        use_mock: bool = None,
        config_manager=None,
        task_logger=None,
    ):
        # Import here to avoid circular imports
        from .config import get_config_manager

        self.config_manager = config_manager or get_config_manager()
        config = self.config_manager.get_config()

        self.base_url = base_url or config.model_base_url
        self.model = model or config.model_name
        self.use_mock = use_mock if use_mock is not None else config.use_mock
        self.client = httpx.AsyncClient(timeout=config.timeout_seconds)

        # 设置日志记录器
        self.task_logger = task_logger  # 任务级别的日志记录器

        if self.task_logger:
            # If a task_logger is provided, use its llm_logger for system-like messages
            self.system_logger = self.task_logger.llm_logger
            # self.task_logger.log_llm("OllamaClient initialized with existing task_logger.") # Optional: log this event
        else:
            # If no task_logger is provided, use a non-file-writing logger for internal/system messages.
            # This logger comes from logging_config and will have a NullHandler by default.
            self.system_logger = get_logger("OllamaClient_Internal")
            self.system_logger.info(
                "OllamaClient initialized without specific task_logger. Internal logs will not create OLLAMA_CLIENT_INTERNAL folder."
            )

    async def chat(
        self, messages: List[Message], stream: bool = False
    ) -> AsyncGenerator[str, None]:
        """Send chat request to Ollama"""

        # 记录LLM输入
        if self.task_logger:
            self.task_logger.log_llm_input(self.model, messages, stream=stream)
        else:
            # 后备使用系统日志记录器
            self.system_logger.info(
                f"🤖 LLM调用开始 - 模型: {self.model}, 流式: {stream}"
            )
            self.system_logger.info(f"📝 输入消息数量: {len(messages)}")

            for i, msg in enumerate(messages, 1):
                self.system_logger.info(f"📋 消息 {i} - 角色: {msg.role}")
                content_preview = (
                    msg.content[:200] + "..." if len(msg.content) > 200 else msg.content
                )
                self.system_logger.info(f"💬 内容预览: {content_preview}")
                if len(msg.content) > 200:
                    self.system_logger.info(f"📊 完整内容长度: {len(msg.content)} 字符")

        if self.use_mock:
            # Mock response for demo purposes
            mock_response = await self._mock_chat_response(messages)

            # 记录模拟响应
            if self.task_logger:
                self.task_logger.log_llm_output(mock_response, mock=True)
            else:
                self.system_logger.info(f"🎭 模拟响应长度: {len(mock_response)} 字符")
                self.system_logger.info(f"📤 模拟响应预览: {mock_response[:200]}...")

            yield mock_response
            return

        url = f"{self.base_url}/api/chat"

        payload = {
            "model": self.model,
            "messages": [msg.model_dump() for msg in messages],
            "stream": stream,
        }

        # 记录请求信息
        if self.task_logger:
            self.task_logger.log_llm(f"🌐 发送请求到: {url}")
            self.task_logger.log_llm(
                f"📦 请求负载大小: {len(json.dumps(payload))} 字符"
            )
        else:
            self.system_logger.info(f"🌐 发送请求到: {url}")
            self.system_logger.info(f"📦 请求负载大小: {len(json.dumps(payload))} 字符")

        response_content = ""

        if stream:
            # 记录流式响应开始
            if self.task_logger:
                self.task_logger.log_llm("🔄 开始流式响应接收")
            else:
                self.system_logger.info("🔄 开始流式响应接收")

            async with self.client.stream("POST", url, json=payload) as response:
                chunk_count = 0
                async for line in response.aiter_lines():
                    if line.strip():
                        try:
                            data = json.loads(line)
                            if "message" in data and "content" in data["message"]:
                                chunk = data["message"]["content"]
                                response_content += chunk
                                chunk_count += 1
                                yield chunk
                        except json.JSONDecodeError:
                            continue

                # 记录流式响应完成
                if self.task_logger:
                    self.task_logger.log_llm(
                        f"📊 流式响应完成 - 接收块数: {chunk_count}"
                    )
                else:
                    self.system_logger.info(
                        f"📊 流式响应完成 - 接收块数: {chunk_count}"
                    )
        else:
            # 记录非流式请求
            if self.task_logger:
                self.task_logger.log_llm("📨 发送非流式请求")
            else:
                self.system_logger.info("📨 发送非流式请求")

            response = await self.client.post(url, json=payload)
            response.raise_for_status()
            data = response.json()
            if "message" in data and "content" in data["message"]:
                response_content = data["message"]["content"]
                yield response_content

        # 记录LLM输出
        if self.task_logger:
            self.task_logger.log_llm_output(response_content)
        else:
            # 后备使用系统日志记录器
            self.system_logger.info(
                f"✅ LLM响应完成 - 总长度: {len(response_content)} 字符"
            )
            response_preview = (
                response_content[:300] + "..."
                if len(response_content) > 300
                else response_content
            )
            self.system_logger.info(f"📤 响应预览: {response_preview}")

            if len(response_content) > 300:
                self.system_logger.info(f"📋 完整响应内容: {response_content}")

            # 分析响应内容类型
            if "<think>" in response_content and "</think>" in response_content:
                self.system_logger.info("🧠 检测到COT思考标签")
            if "[" in response_content and "]" in response_content:
                self.system_logger.info("📋 检测到JSON格式内容")
            if "```" in response_content:
                self.system_logger.info("💻 检测到代码块内容")

    async def generate_plan(self, task_description: str) -> List[Dict]:
        """生成任务执行计划"""
        print(f"🧠 [模型调用] 使用 {self.model} 生成执行计划")

        # 记录任务规划开始
        if self.task_logger:
            self.task_logger.log_llm(f"📋 开始任务规划 - 模型: {self.model}")
            self.task_logger.log_llm(f"🎯 用户任务: {task_description}")
        else:
            self.system_logger.info(f"📋 开始任务规划 - 模型: {self.model}")
            self.system_logger.info(f"🎯 用户任务: {task_description}")

        system_prompt = """你是一个AI任务规划器。根据用户请求，将其分解为具体的可执行步骤。

请以JSON数组格式返回步骤，每个步骤包含：
- "id": 唯一步骤标识符
- "action": 要执行的动作（通常是"execute_locally"）
- "tool": 要使用的工具
- "parameters": 工具参数（必须包含工具所需的所有必需参数）
- "description": 人类可读的描述

可用工具及其参数：

1. calculator（计算器）
   - 必需参数: expression（数学表达式字符串，使用Python语法，如 'pi*5**2' 表示圆面积）
   - 可选参数: precision（精度，默认6）
   - 支持函数: sqrt(), sin(), cos(), tan(), log(), exp(), pi, e
   - 示例: "pi*10**2" (圆面积), "sqrt(144)" (平方根), "2*pi*5" (圆周长)

2. web_search（网络搜索）
   - 必需参数: query（搜索查询字符串）
   - 可选参数: max_results（最大结果数，默认5）, language（语言，默认"en"）

3. file_ops（文件操作）
   - 必需参数: operation（操作类型："read", "write", "list", "mkdir", "delete"）, path（文件路径）
   - 可选参数: content（写入内容，用于write操作）, encoding（编码，默认"utf-8"）

重要规则：
1. 只有在用户明确要求创建、写入、保存文件时才使用file_ops工具
2. 对于搜索、查询、分析类任务，通常只需要web_search工具
3. 不要为了"保存结果"而自动添加文件操作，除非用户明确要求
4. 如果任务是"搜索并总结"，只需要搜索步骤，总结会在搜索结果中自动完成

示例:
[
    {
        "id": 1,
        "action": "execute_locally",
        "tool": "calculator",
        "parameters": {"expression": "pi * 5", "precision": 4},
        "description": "计算圆周率乘以5"
    },
    {
        "id": 2,
        "action": "execute_locally",
        "tool": "web_search",
        "parameters": {"query": "圆周率历史", "max_results": 3},
        "description": "搜索圆周率的历史信息"
    }
]

请确保：
1. 每个工具调用都包含所有必需参数
2. 参数值要具体和准确
3. 描述使用中文
4. JSON格式正确
5. 不要无必要地添加文件操作步骤"""

        messages = [
            Message(role="system", content=system_prompt),
            Message(role="user", content=f"任务: {task_description}"),
        ]

        # 记录规划请求
        if self.task_logger:
            self.task_logger.log_llm(
                f"📤 发送规划请求 - 系统提示词长度: {len(system_prompt)} 字符"
            )
            self.task_logger.log_llm(f"📝 用户消息: 任务: {task_description}")
        else:
            self.system_logger.info(
                f"📤 发送规划请求 - 系统提示词长度: {len(system_prompt)} 字符"
            )
            self.system_logger.info(f"📝 用户消息: 任务: {task_description}")

        try:
            response_content = ""
            async for chunk in self.chat(messages):
                response_content += chunk

            print(
                f"✅ [模型响应] {self.model} 完成计划生成，响应长度: {len(response_content)} 字符"
            )
        except Exception as e:
            print(f"❌ [模型调用失败] 无法连接到模型 {self.model}: {str(e)}")

            # 记录错误
            if self.task_logger:
                self.task_logger.log_llm(f"❌ 模型调用失败: {str(e)}", "ERROR")
            else:
                self.system_logger.error(f"❌ 模型调用失败: {str(e)}")

            # 直接返回后备方案
            print("🔄 [后备方案] 使用简单计划替代")
            fallback_plan = [
                {
                    "id": 1,
                    "action": "execute_locally",
                    "tool": "calculator",
                    "parameters": {"expression": "pi*3**2", "precision": 6},
                    "description": "计算半径为3的圆面积",
                },
                {
                    "id": 2,
                    "action": "execute_locally",
                    "tool": "calculator",
                    "parameters": {"expression": "2*pi*3", "precision": 6},
                    "description": "计算半径为3的圆周长",
                },
            ]

            if self.task_logger:
                self.task_logger.log_llm(f"🔄 后备计划: {fallback_plan}")
            else:
                self.system_logger.info(f"🔄 后备计划: {fallback_plan}")

            return fallback_plan

        # 记录规划响应
        if self.task_logger:
            self.task_logger.log_llm(
                f"📥 规划响应接收完成 - 长度: {len(response_content)} 字符"
            )
        else:
            self.system_logger.info(
                f"📥 规划响应接收完成 - 长度: {len(response_content)} 字符"
            )

        # 显示模型思考过程
        self._display_thinking_process(response_content)

        # Try to extract JSON from the response
        try:
            # Find JSON array in the response
            start = response_content.find("[")
            end = response_content.rfind("]") + 1
            if start != -1 and end != 0:
                json_str = response_content[start:end]
                # 记录JSON提取
                if self.task_logger:
                    self.task_logger.log_llm(
                        f"🔍 提取JSON字符串 - 长度: {len(json_str)} 字符"
                    )
                    self.task_logger.log_llm(f"📋 JSON内容: {json_str}")
                else:
                    self.system_logger.info(
                        f"🔍 提取JSON字符串 - 长度: {len(json_str)} 字符"
                    )
                    self.system_logger.info(f"📋 JSON内容: {json_str}")

                plan_data = json.loads(json_str)
                print(
                    f"📊 [计划解析] 成功解析 {len(plan_data)} 个步骤 (模型: {self.model})"
                )

                # 记录解析成功
                if self.task_logger:
                    self.task_logger.log_llm(
                        f"✅ JSON解析成功 - 步骤数: {len(plan_data)}"
                    )
                    for i, step in enumerate(plan_data, 1):
                        self.task_logger.log_llm(
                            f"📋 步骤 {i}: {step.get('description', '无描述')} - 工具: {step.get('tool', '未知')}"
                        )
                else:
                    self.system_logger.info(
                        f"✅ JSON解析成功 - 步骤数: {len(plan_data)}"
                    )
                    for i, step in enumerate(plan_data, 1):
                        self.system_logger.info(
                            f"📋 步骤 {i}: {step.get('description', '无描述')} - 工具: {step.get('tool', '未知')}"
                        )

                return plan_data
        except (json.JSONDecodeError, ValueError) as e:
            print(f"❌ [解析错误] 模型 {self.model} 响应格式错误，无法解析JSON")

            # 记录解析错误
            if self.task_logger:
                self.task_logger.log_llm(f"❌ JSON解析失败: {str(e)}", "ERROR")
                self.task_logger.log_llm(
                    f"📋 原始响应内容: {response_content}", "ERROR"
                )
            else:
                self.system_logger.error(f"❌ JSON解析失败: {str(e)}")
                self.system_logger.error(f"📋 原始响应内容: {response_content}")
            pass

        # 后备方案：创建简单计划
        if self.task_logger:
            self.task_logger.log_llm("⚠️ 使用后备方案创建简单计划", "WARNING")
        else:
            self.system_logger.warning("⚠️ 使用后备方案创建简单计划")

        fallback_plan = [
            {
                "id": 1,
                "action": "analyze",
                "tool": "general",
                "parameters": {"task": task_description},
                "description": f"分析并执行: {task_description}",
            }
        ]

        if self.task_logger:
            self.task_logger.log_llm(f"🔄 后备计划: {fallback_plan}")
        else:
            self.system_logger.info(f"🔄 后备计划: {fallback_plan}")

        return fallback_plan

    async def _mock_chat_response(self, messages: List[Message]) -> str:
        """Generate mock response for demo purposes"""
        import asyncio

        await asyncio.sleep(0.5)  # Simulate network delay

        # Get the last user message
        user_message = ""
        for msg in reversed(messages):
            if msg.role == "user":
                user_message = msg.content.lower()
                break

        # 根据任务类型生成适当的模拟响应
        if (
            "计算" in user_message
            or "算" in user_message
            or "calculate" in user_message
            or "math" in user_message
        ):
            return """[
    {
        "id": 1,
        "action": "execute_locally",
        "tool": "calculator",
        "parameters": {"expression": "sqrt(144)", "precision": 2},
        "description": "计算平方根"
    }
]"""
        elif (
            "搜索" in user_message or "查找" in user_message or "search" in user_message
        ):
            # 提取搜索关键词
            if "python" in user_message.lower():
                query = "Python编程教程"
            elif "javascript" in user_message.lower():
                query = "JavaScript编程教程"
            elif "天气" in user_message:
                query = "北京天气情况"
            else:
                # 从用户消息中提取关键词
                query = (
                    user_message.replace("搜索", "")
                    .replace("查找", "")
                    .replace("并总结", "")
                    .replace("并解读", "")
                    .strip()
                )
                if not query:
                    query = "编程教程"

            return f"""[
    {{
        "id": 1,
        "action": "execute_locally",
        "tool": "web_search",
        "parameters": {{"query": "{query}", "max_results": 5, "language": "zh"}},
        "description": "搜索{query}相关信息"
    }}
]"""
        elif (
            "文件" in user_message
            or "创建" in user_message
            or "file" in user_message
            or "create" in user_message
        ):
            return """[
    {
        "id": 1,
        "action": "execute_locally",
        "tool": "file_ops",
        "parameters": {"operation": "write", "path": "演示文件.txt", "content": "演示内容"},
        "description": "创建演示文件"
    }
]"""
        elif ("列出" in user_message and "目录" in user_message) or (
            "list" in user_message and "directory" in user_message
        ):
            return """[
    {
        "id": 1,
        "action": "execute_locally",
        "tool": "file_ops",
        "parameters": {"operation": "list", "path": "."},
        "description": "列出当前目录文件"
    }
]"""
        else:
            # 通用响应
            return """[
    {
        "id": 1,
        "action": "analyze",
        "tool": "general",
        "parameters": {"task": "通用任务执行"},
        "description": "执行请求的任务"
    }
]"""

    def _display_thinking_process(self, response_content: str):
        """显示模型的思考过程（仅对COT模型且配置允许时显示）"""
        # 检查是否应该显示思考过程
        try:
            if not self.config_manager.should_show_thinking(self.model):
                return
        except AttributeError:
            # 如果config_manager不可用，默认不显示思考过程
            return

        from rich.console import Console
        from rich.panel import Panel

        console = Console()

        # 提取思考过程（如果存在）
        thinking_start = response_content.find("<think>")
        thinking_end = response_content.find("</think>")

        if thinking_start != -1 and thinking_end != -1:
            thinking_content = response_content[
                thinking_start + 7 : thinking_end
            ].strip()
            if thinking_content:
                console.print(
                    Panel(
                        thinking_content,
                        title="🧠 AI 思考过程",
                        border_style="yellow",
                        expand=False,
                    )
                )

        # 如果没有明确的思考标签，尝试提取非JSON部分作为思考过程
        elif "[" in response_content:
            json_start = response_content.find("[")
            pre_json = response_content[:json_start].strip()
            if pre_json and len(pre_json) > 10:  # 只显示有意义的思考内容
                console.print(
                    Panel(
                        pre_json,
                        title="🧠 AI 分析过程",
                        border_style="yellow",
                        expand=False,
                    )
                )

    async def generate_summary(
        self, task_description: str, plan_results: List[Dict]
    ) -> str:
        """生成任务执行总结（Augment Code风格）"""

        print(f"📝 [生成总结] 使用 {self.model} 生成任务总结")

        # 分析执行结果
        successful_count = sum(
            1 for result in plan_results if result.get("success", False)
        )
        total_count = len(plan_results)
        has_failures = any(not result.get("success", False) for result in plan_results)

        # 提取关键结果数据
        key_results = []
        calculation_results = []
        search_results = []
        file_operations = []

        for i, result in enumerate(plan_results, 1):
            if result.get("success", False):
                output = result.get("result", "")
                desc = result.get("description", f"步骤 {i}")

                # 分类处理不同类型的结果（优先级：搜索 > 文件 > 计算 > 其他）
                if (
                    "搜索" in desc
                    or "查询" in desc
                    or ("搜索查询" in str(output) and "找到" in str(output))
                ):
                    search_results.append({"step": i, "desc": desc, "result": output})
                elif "文件" in desc or (
                    "file" in str(output) and "operation" in str(output)
                ):
                    file_operations.append({"step": i, "desc": desc, "result": output})
                elif (
                    "计算" in desc
                    or "验证" in desc
                    or ("calculator" in str(output) and "expression" in str(output))
                ):
                    calculation_results.append(
                        {"step": i, "desc": desc, "result": output}
                    )
                else:
                    key_results.append({"step": i, "desc": desc, "result": output})

        # 构建详细总结
        summary_parts = []

        # 1. 执行状态概览
        if successful_count == total_count:
            summary_parts.append("✅ **任务执行成功**")
        elif successful_count > 0:
            summary_parts.append(
                f"⚠️ **任务部分完成** ({successful_count}/{total_count} 步骤成功)"
            )
        else:
            summary_parts.append("❌ **任务执行失败**")

        summary_parts.append(f"📋 **原始任务**: {task_description}")

        # 2. 详细执行结果
        if calculation_results:
            summary_parts.append("\n🧮 **计算结果**:")
            for calc in calculation_results:
                # 尝试解析计算结果
                result_str = str(calc["result"])
                if "expression" in result_str and "result" in result_str:
                    try:
                        # 尝试解析字典格式的结果
                        if result_str.startswith("{") and result_str.endswith("}"):
                            calc_data = eval(result_str)  # 简单解析
                            expr = calc_data.get("expression", "")
                            value = calc_data.get("result", "")
                            desc_text = calc_data.get("description", "")

                            summary_parts.append(f"  • **{expr}** = **{value}**")
                            if desc_text and "这是" in desc_text:
                                # 提取描述中的解释部分
                                lines = desc_text.split("\\n")  # 处理转义的换行符
                                explanation = next(
                                    (line for line in lines if "这是" in line), ""
                                )
                                if explanation:
                                    summary_parts.append(f"    {explanation}")
                        else:
                            summary_parts.append(
                                f"  • {calc['desc']}: {result_str[:200]}"
                            )
                    except Exception as e:
                        summary_parts.append(f"  • {calc['desc']}: {result_str[:200]}")
                else:
                    summary_parts.append(f"  • {calc['desc']}: {result_str[:200]}")

        if search_results:
            summary_parts.append("\n🔍 **搜索结果与内容分析**:")
            for search in search_results:
                result_str = str(search["result"])
                desc = search["desc"]

                # 提取搜索结果摘要
                if "搜索查询" in result_str and "找到" in result_str:
                    lines = result_str.split("\n")

                    # 提取查询和结果数量
                    query_line = next(
                        (line for line in lines if "搜索查询:" in line), ""
                    )
                    count_line = next(
                        (
                            line
                            for line in lines
                            if "找到" in line and "个相关结果" in line
                        ),
                        "",
                    )

                    if query_line and count_line:
                        query = query_line.replace("搜索查询: ", "").strip()
                        summary_parts.append(f"  • **查询主题**: {query}")
                        summary_parts.append(f"  • **搜索结果**: {count_line}")

                        # 检查是否有详细网页内容
                        if "详细网页内容" in result_str:
                            summary_parts.append("  • **内容分析**:")

                            # 提取网页内容进行分析
                            content_section = (
                                result_str.split("详细网页内容")[1]
                                if "详细网页内容" in result_str
                                else ""
                            )

                            if content_section:
                                # 提取关键信息
                                web_contents = self._extract_web_content_summary(
                                    content_section, query
                                )
                                for content_summary in web_contents:
                                    summary_parts.append(f"    - {content_summary}")
                            else:
                                summary_parts.append(
                                    "    - 已获取网页详细内容，但内容较长，请查看完整搜索结果"
                                )
                        else:
                            # 如果没有详细内容，显示标题
                            result_titles = []
                            for i, line in enumerate(lines):
                                if (
                                    line.strip()
                                    and ". " in line
                                    and "来源:" not in line
                                    and "摘要:" not in line
                                    and "链接:" not in line
                                ):
                                    if len(result_titles) < 3:
                                        title = line.strip()
                                        if title[0].isdigit() and ". " in title:
                                            title = (
                                                title.split(". ", 1)[1]
                                                if ". " in title
                                                else title
                                            )
                                        result_titles.append(title)

                            if result_titles:
                                summary_parts.append("  • **主要结果**:")
                                for title in result_titles:
                                    summary_parts.append(f"    - {title}")
                    else:
                        summary_parts.append(f"  • {desc}: 搜索完成")
                else:
                    summary_parts.append(f"  • {desc}: 搜索完成")

        if file_operations:
            summary_parts.append("\n📁 **文件操作**:")
            for file_op in file_operations:
                summary_parts.append(f"  • {file_op['desc']}: 已完成")

        if key_results:
            summary_parts.append("\n📊 **其他结果**:")
            for result in key_results:
                result_str = str(result["result"])
                # 限制显示长度，避免过长的输出
                if len(result_str) > 100:
                    result_str = result_str[:100] + "..."
                summary_parts.append(f"  • {result['desc']}: {result_str}")

        # 3. 失败信息（如果有）
        if has_failures:
            summary_parts.append("\n❌ **失败步骤**:")
            for i, result in enumerate(plan_results, 1):
                if not result.get("success", False):
                    error = result.get("error", "未知错误")
                    desc = result.get("description", f"步骤 {i}")
                    summary_parts.append(f"  • 步骤 {i} ({desc}): {error}")

        # 4. 基于网页内容的深度分析（如果有搜索结果）
        if search_results:
            content_analysis = self._generate_content_analysis(
                search_results, task_description
            )
            if content_analysis:
                summary_parts.append(f"\n📖 **内容解读与分析**:")
                summary_parts.append(content_analysis)

        # 5. 总结建议
        summary_parts.append(
            f"\n📈 **执行统计**: {successful_count}/{total_count} 步骤成功完成"
        )

        if successful_count == total_count:
            summary_parts.append("🎉 所有步骤均已成功执行，任务圆满完成！")
        elif successful_count > 0:
            summary_parts.append("💡 部分步骤成功完成，建议检查失败步骤并重试。")
        else:
            summary_parts.append("🔧 建议检查工具配置和参数设置后重新执行。")

        return "\n".join(summary_parts)

    def _extract_web_content_summary(self, content_section: str, query: str) -> list:
        """从网页内容中提取关键摘要信息"""
        summaries = []

        # 按网页分割内容
        web_pages = content_section.split("**网页")

        for i, page_content in enumerate(web_pages[1:], 1):  # 跳过第一个空分割
            if not page_content.strip():
                continue

            lines = page_content.split("\n")
            title = ""

            # 提取标题
            for line in lines[:5]:
                if line.strip() and ":" in line and not line.startswith("链接"):
                    title = line.split(":", 1)[1].strip()
                    break

            # 提取所有有意义的内容行
            content_lines = []
            for line in lines:
                line = line.strip()
                if (
                    line
                    and len(line) > 15  # 过滤太短的行
                    and not line.startswith("链接:")
                    and not line.startswith("-")
                    and not line.startswith("**网页")
                    and not line.startswith("网页标题:")
                    and not line.startswith("网页链接:")
                    and not line.startswith("内容长度:")
                ):
                    content_lines.append(line)

            # 取更多内容，不要过度截断
            if content_lines:
                # 取前8-10段有意义的内容，而不是只取3段
                content = " ".join(content_lines[:10])

                # 增加内容长度限制到800字符
                if len(content) > 800:
                    content = content[:800] + "..."

                if title and content:
                    summaries.append(f"**{title}**: {content}")
                elif content:
                    # 如果没有标题，使用网页序号
                    summaries.append(f"**网页内容 {i}**: {content}")

            # 增加摘要数量限制到4个
            if len(summaries) >= 4:
                break

        if not summaries:
            summaries.append("已成功获取网页详细内容，但内容解析遇到问题")

        return summaries

    def _generate_content_analysis(
        self, search_results: list, task_description: str
    ) -> str:
        """基于搜索结果中的网页内容生成深度分析"""
        analysis_parts = []

        for search in search_results:
            result_str = str(search["result"])

            # 检查是否包含详细网页内容
            if "详细网页内容" in result_str:
                # 提取网页内容部分
                content_section = (
                    result_str.split("详细网页内容")[1]
                    if "详细网页内容" in result_str
                    else ""
                )

                if content_section:
                    # 提取关键信息进行分析
                    key_points = self._extract_key_points(
                        content_section, task_description
                    )
                    if key_points:
                        analysis_parts.extend(key_points)

        if analysis_parts:
            # 生成更详细的综合分析
            analysis = "基于获取的网页详细内容，以下是深度解读和分析：\n\n"

            # 显示更多关键要点，增加到10个
            for i, point in enumerate(analysis_parts[:10], 1):
                analysis += f"**{i}. {point}**\n\n"

            # 根据任务类型生成更具体的分析
            if "agent" in task_description.lower() or "智能体" in task_description:
                analysis += self._generate_agent_specific_analysis(analysis_parts)
            elif "祖冲之" in task_description:
                analysis += "**综合分析**: 祖冲之是中国古代杰出的数学家和天文学家，其在圆周率计算方面的贡献具有重要的历史意义和科学价值。他的密率355/113在当时是世界上最精确的圆周率近似值，比欧洲早了一千多年。"
            elif "实现" in task_description and (
                "思路" in task_description or "方法" in task_description
            ):
                analysis += self._generate_implementation_analysis(
                    analysis_parts, task_description
                )
            else:
                analysis += "**综合分析**: 基于多个权威来源的详细内容整合，为您提供了全面深入的信息分析。上述要点涵盖了查询主题的核心概念、技术细节和实践指导。"

            return analysis

        return ""

    def _extract_key_points(self, content_section: str, task_description: str) -> list:
        """从网页内容中提取关键要点"""
        key_points = []

        # 按网页分割内容
        web_pages = content_section.split("**网页")

        for page_content in web_pages[1:]:  # 跳过第一个空分割
            if not page_content.strip():
                continue

            lines = page_content.split("\n")

            # 提取有意义的段落
            meaningful_lines = []
            for line in lines:
                line = line.strip()
                if (
                    line
                    and len(line) > 15  # 降低长度要求
                    and not line.startswith("链接:")
                    and not line.startswith("-")
                    and not line.startswith("**网页")
                    and not line.startswith("网页标题:")
                    and not line.startswith("网页链接:")
                    and not line.startswith("内容长度:")
                ):
                    meaningful_lines.append(line)

            # 根据任务类型提取相关要点
            if "祖冲之" in task_description:
                for line in meaningful_lines[:5]:  # 增加到5个段落
                    if any(
                        keyword in line
                        for keyword in [
                            "祖冲之",
                            "圆周率",
                            "数学",
                            "天文",
                            "密率",
                            "约率",
                        ]
                    ):
                        # 增加内容长度到400字符
                        key_points.append(
                            line[:400] + "..." if len(line) > 400 else line
                        )
            elif "agent" in task_description.lower() or "智能体" in task_description:
                # Agent相关的关键词
                agent_keywords = [
                    "agent",
                    "智能体",
                    "架构",
                    "框架",
                    "实现",
                    "设计",
                    "模块",
                    "技术",
                    "开发",
                    "流程",
                    "核心",
                    "功能",
                    "系统",
                    "平台",
                ]
                for line in meaningful_lines[:6]:  # 增加到6个段落
                    if any(keyword in line.lower() for keyword in agent_keywords):
                        key_points.append(
                            line[:500] + "..." if len(line) > 500 else line
                        )
                    elif len(meaningful_lines) <= 3:  # 如果内容较少，也包含进来
                        key_points.append(
                            line[:500] + "..." if len(line) > 500 else line
                        )
            else:
                # 通用提取逻辑 - 大幅增加内容
                for line in meaningful_lines[:8]:  # 增加到8个有意义的段落
                    key_points.append(line[:500] + "..." if len(line) > 500 else line)

            # 增加每个网页的要点数量限制
            if len(key_points) >= 8:
                break

        return key_points

    def _generate_agent_specific_analysis(self, analysis_parts: list) -> str:
        """生成Agent特定的分析"""
        analysis = "\n**Agent实现思路综合分析**:\n\n"

        # 分析架构设计
        architecture_points = [
            p
            for p in analysis_parts
            if any(keyword in p.lower() for keyword in ["架构", "设计", "框架", "模块"])
        ]
        if architecture_points:
            analysis += "**架构设计要点**:\n"
            for point in architecture_points[:3]:
                analysis += (
                    f"• {point[:300]}...\n" if len(point) > 300 else f"• {point}\n"
                )
            analysis += "\n"

        # 分析技术实现
        tech_points = [
            p
            for p in analysis_parts
            if any(keyword in p.lower() for keyword in ["技术", "实现", "开发", "编程"])
        ]
        if tech_points:
            analysis += "**技术实现要点**:\n"
            for point in tech_points[:3]:
                analysis += (
                    f"• {point[:300]}...\n" if len(point) > 300 else f"• {point}\n"
                )
            analysis += "\n"

        # 分析核心功能
        function_points = [
            p
            for p in analysis_parts
            if any(keyword in p.lower() for keyword in ["功能", "能力", "特性", "核心"])
        ]
        if function_points:
            analysis += "**核心功能要点**:\n"
            for point in function_points[:3]:
                analysis += (
                    f"• {point[:300]}...\n" if len(point) > 300 else f"• {point}\n"
                )
            analysis += "\n"

        analysis += "**总结**: Agent实现涉及多个层面的技术整合，包括架构设计、核心算法、功能模块和工程实践。成功的Agent系统需要在感知、推理、决策和执行等环节形成有效的闭环。"

        return analysis

    def _generate_implementation_analysis(
        self, analysis_parts: list, task_description: str
    ) -> str:
        """生成实现思路的分析"""
        analysis = "\n**实现思路深度分析**:\n\n"

        # 提取方法论相关内容
        methodology_points = [
            p
            for p in analysis_parts
            if any(
                keyword in p.lower()
                for keyword in ["方法", "步骤", "流程", "过程", "思路"]
            )
        ]
        if methodology_points:
            analysis += "**实现方法论**:\n"
            for point in methodology_points[:4]:
                analysis += (
                    f"• {point[:400]}...\n" if len(point) > 400 else f"• {point}\n"
                )
            analysis += "\n"

        # 提取技术细节
        detail_points = [
            p
            for p in analysis_parts
            if any(
                keyword in p.lower()
                for keyword in ["技术", "算法", "模型", "工具", "框架"]
            )
        ]
        if detail_points:
            analysis += "**技术实现细节**:\n"
            for point in detail_points[:4]:
                analysis += (
                    f"• {point[:400]}...\n" if len(point) > 400 else f"• {point}\n"
                )
            analysis += "\n"

        analysis += "**总结**: 基于上述分析，实现思路需要综合考虑理论基础、技术选型、架构设计和工程实践等多个维度，形成系统性的解决方案。"

        return analysis

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()
