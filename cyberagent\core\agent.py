"""
AutoAgent - Unified Agent Interface

This module provides the main AutoAgent class that serves as the unified
interface for both terminal and web interactions.
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional, List
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt

from .model import OllamaClient
from .planner import TaskPlanner, TaskStatus
from .executor import ExecutionEngine
from .config import get_config_manager
from .session import get_session_manager
from .api import (
    AgentAPIInterface,
    TaskRequest,
    TaskResponse,
    TaskStep,
    SystemStatus,
    ToolInfo,
    ModelInfo,
    SessionInfo,
    APIResponse,
    create_task_step,
    create_tool_info,
)
from execution.tools.manager import ToolManager


class CyberAgent(AgentAPIInterface):
    """
    Unified AutoAgent class for autonomous task planning and execution.

    This class provides a standardized interface that can be used by both
    terminal and web interfaces, ensuring consistent behavior across all
    interaction modes.
    """

    def __init__(self, model: Optional[str] = None, config_path: Optional[str] = None):
        """
        Initialize the AutoAgent.

        Args:
            model: Override default model name
            config_path: Path to configuration file
        """
        self.console = Console()

        # Initialize configuration and session management
        self.config_manager = get_config_manager(config_path)
        self.session_manager = get_session_manager()

        # Override model if specified
        if model:
            self.config_manager.get_config().model_name = model

        # Initialize core components
        self.model_client = OllamaClient(config_manager=self.config_manager)
        self.tool_manager = ToolManager()
        self.planner = TaskPlanner(self.model_client)
        # 终端模式不需要WebSocket回调
        self.executor = ExecutionEngine(self.tool_manager, websocket_callback=None)

    async def process_task(self, request: TaskRequest) -> TaskResponse:
        """
        Process a task and return structured results.

        This is the main API method that can be used by both terminal and web interfaces.

        Args:
            task_description: The task to execute
            task_id: Optional task ID for tracking

        Returns:
            dict: Structured result containing success status, results, and metadata
        """
        try:
            # Extract task information from request
            task_description = request.task
            task_id = (
                request.session_id or f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )

            # Override model if specified in request
            if request.model and request.model != self.model_client.model:
                self.model_client.model = request.model
                self.config_manager.get_config().model_name = request.model

            # Record user message
            self.session_manager.add_message("user", task_description, task_id)

            # Get related historical tasks for context
            related_tasks = self.session_manager.get_related_tasks(task_description)
            enhanced_task = task_description
            if related_tasks:
                context_info = f"\n\n相关历史任务:\n" + "\n".join(
                    [
                        f"- {task.description} (状态: {task.status})"
                        for task in related_tasks[:3]
                    ]
                )
                enhanced_task += context_info

            # Create execution plan
            plan = await self.planner.create_plan(enhanced_task, task_id)

            # Record task context
            task_context = self.session_manager.add_task_context(
                task_id=task_id,
                description=task_description,
                plan_steps=[
                    {
                        "id": step.id,
                        "tool": step.tool,
                        "description": step.description,
                        "parameters": step.parameters,
                    }
                    for step in plan.steps
                ],
            )

            # Update related tasks separately
            if related_tasks:
                task_context.related_tasks = [t.task_id for t in related_tasks]

            # Execute the plan
            success = await self.executor.execute_plan(plan)

            # Collect execution results
            plan_results = []
            for step in plan.steps:
                plan_results.append(
                    {
                        "step_id": step.id,
                        "tool": step.tool,
                        "description": step.description,
                        "status": step.status.value,
                        "result": getattr(step, "result", None),
                        "error": getattr(step, "error", None),
                    }
                )

            # Update task status
            final_status = "completed" if success else "failed"
            self.session_manager.update_task_status(task_id, final_status)

            # Generate intelligent summary
            summary = await self._generate_summary(task_description, plan_results)

            # Record assistant reply
            self.session_manager.add_message("assistant", summary, task_id)

            return TaskResponse(
                success=success,
                task_id=task_id,
                message="任务执行完成",
                summary=summary,
                steps=[],  # Convert plan_results to TaskStep format if needed
                related_tasks=[t.task_id for t in related_tasks],
                metadata={"plan_results": plan_results},
            )

        except Exception as e:
            # Update task status to error
            if task_id:
                self.session_manager.update_task_status(task_id, "error")

            error_msg = f"处理任务时出错: {e}"
            return TaskResponse(
                success=False,
                task_id=task_id or "unknown",
                message=error_msg,
                error=error_msg,
            )

    async def _generate_summary(self, task_description: str, plan_results: list) -> str:
        """Generate intelligent summary of task execution"""
        try:
            # 记录总结生成开始
            print(f"📝 [生成总结] 开始生成任务执行总结")

            # Prepare summary prompt
            results_text = "\n".join(
                [
                    f"步骤{r['step_id']}: {r['description']} - {r['status']}"
                    + (f" (结果: {r['result'][:100]}...)" if r.get("result") else "")
                    for r in plan_results
                ]
            )

            summary_prompt = f"""
            任务: {task_description}

            执行结果:
            {results_text}

            请生成一个简洁的任务执行总结，包括:
            1. 任务完成情况
            2. 主要结果
            3. 如有问题，简要说明

            总结应该简洁明了，不超过200字。
            """

            # Use model to generate summary
            from .model import Message

            messages = [
                Message(
                    role="system",
                    content="你是一个任务执行总结助手，负责生成简洁明了的任务总结。",
                ),
                Message(role="user", content=summary_prompt),
            ]

            print(f"📤 [总结生成] 发送总结请求到模型 {self.model_client.model}")

            summary = ""
            async for chunk in self.model_client.chat(messages):
                summary += chunk

            print(f"✅ [总结完成] 生成总结长度: {len(summary)} 字符")

            return summary.strip() if summary.strip() else "任务执行完成"

        except Exception as e:
            print(f"❌ [总结失败] 生成总结时出错: {e}")
            return f"任务执行完成，但生成总结时出错: {e}"

    async def start_interactive_mode(self):
        """Start interactive terminal mode"""
        # Get available tools for display
        tools = self.tool_manager.list_tools()
        tool_names = [f"{tool.name}（{tool.description}）" for tool in tools]
        tools_text = ", ".join(tool_names)

        self.console.print(
            Panel.fit(
                "🤖 AutoAgent - 自主任务规划与执行框架\n\n"
                "此框架展示了现代AI代理的核心工作原理:\n"
                "1. 🤔 任务规划 - 将用户请求分解为步骤\n"
                "2. 🔧 工具选择 - 为每个步骤选择合适的工具\n"
                "3. 🚀 执行 - 使用可用工具执行步骤\n"
                "4. 📊 监控 - 跟踪进度并处理错误\n\n"
                f"可用工具: {tools_text}\n"
                "输入 'help' 查看命令或 'quit' 退出。",
                title="欢迎使用 AutoAgent",
                border_style="blue",
            )
        )

        while True:
            try:
                user_input = Prompt.ask("\n[bold blue]请输入您的任务[/bold blue]")

                if not user_input.strip():
                    continue

                # Handle special commands
                command_result = await self._handle_special_commands(
                    user_input.lower().strip()
                )

                if command_result == "quit":
                    # Cleanup resources before exiting
                    await self.cleanup()
                    break
                elif command_result == "continue":
                    continue

                # Process the task (command_result == "process")
                from .api import TaskRequest

                request = TaskRequest(task=user_input)
                result = await self.process_task(request)

                # Display results
                if result.success:
                    self.console.print(f"\n✅ 任务完成")
                    self.console.print(
                        Panel(
                            result.summary or result.message,
                            title="执行总结",
                            border_style="green",
                        )
                    )
                else:
                    self.console.print(f"\n❌ 任务失败")
                    self.console.print(
                        Panel(
                            result.error or "未知错误",
                            title="错误信息",
                            border_style="red",
                        )
                    )

            except KeyboardInterrupt:
                self.console.print("\n👋 再见！")
                await self.cleanup()
                break
            except Exception as e:
                self.console.print(f"💥 发生错误: {e}")

    async def _handle_special_commands(self, command: str) -> str:
        """
        Handle special commands

        Returns:
            'quit' - if should exit the program
            'continue' - if command was handled and should continue
            'process' - if should process as normal task
        """
        if command in ["quit", "exit", "q", "退出", "结束"]:
            self.console.print("👋 再见！")
            return "quit"
        elif command in ["help", "帮助"]:
            await self._show_help()
            return "continue"
        elif command in ["tools", "工具"]:
            await self._show_tools()
            return "continue"
        elif command in ["settings", "设置"]:
            await self._show_settings()
            return "continue"
        elif command in ["clear session", "清空会话"]:
            self.session_manager.clear_current_session()
            self.console.print("🗑️ 当前会话已清空")
            return "continue"
        elif command in ["history", "历史", "session", "会话"]:
            await self._show_session_info()
            return "continue"

        return "process"

    async def _show_help(self):
        """Show help information"""
        help_text = """
可用命令:
• help/帮助 - 显示此帮助信息
• tools/工具 - 列出可用工具
• settings/设置 - 显示当前配置设置
• history/历史/session/会话 - 显示会话信息
• clear session/清空会话 - 清空当前会话
• quit/exit/q/退出/结束 - 退出程序

您可以尝试的示例任务:

🔧 基础功能:
• "搜索Python异步编程教程"
• "计算半径为5的圆的面积"
• "创建一个名为test.txt的文件，包含一些内容"
• "列出当前目录的文件"
• "获取网页内容并分析"

🌐 信息获取:
• "搜索最新的AI技术发展"
• "查找机器学习相关资料"
• "获取某个网站的详细内容"

特性:
• 🔗 上下文关联: 任务间会自动关联相关历史
• 🌐 网页内容获取: 搜索结果会自动获取网页详细内容
• 💾 会话管理: 对话历史和任务状态持久化保存
• 🔧 工具集成: 文件操作、网络搜索、计算器等实用工具
        """
        self.console.print(Panel(help_text, title="帮助信息", border_style="cyan"))

    async def _show_tools(self):
        """Show available tools"""
        tools = self.tool_manager.list_tools()

        # Group tools by category
        categories = {}
        for tool in tools:
            category = tool.category
            if category not in categories:
                categories[category] = []
            categories[category].append(tool)

        # Build display text
        tools_text = ""
        for category, category_tools in categories.items():
            category_name = {
                "general": "🔧 通用工具",
                "security": "🛡️ 安全工具",
                "analysis": "📊 分析工具",
                "network": "🌐 网络工具",
            }.get(category, f"📁 {category.title()}")

            tools_text += f"{category_name}:\n"
            for tool in category_tools:
                tools_text += f"  • {tool.name}: {tool.description}\n"
            tools_text += "\n"

        self.console.print(
            Panel(tools_text.strip(), title="可用工具", border_style="yellow")
        )

    async def _show_settings(self):
        """Show current settings"""
        config = self.config_manager.get_config()
        settings_text = f"""
模型设置:
• 当前模型: {config.model_name}
• 服务器地址: {config.model_base_url}
• 模拟模式: {"开启" if config.use_mock else "关闭"}

显示设置:
• 思考过程: {"显示" if config.show_thinking_process else "隐藏"}
• 详细日志: {"开启" if config.show_detailed_logs else "关闭"}

工具设置:
• 最大重试次数: {config.max_retries}
• 超时时间: {config.timeout_seconds}秒
        """
        self.console.print(
            Panel(settings_text, title="当前设置", border_style="magenta")
        )

    async def _show_session_info(self):
        """Show session information"""
        session = self.session_manager.get_current_session()

        info_text = f"""
会话ID: {session.session_id}
创建时间: {session.created_at.strftime("%Y-%m-%d %H:%M:%S")}
最后活动: {session.last_activity.strftime("%Y-%m-%d %H:%M:%S")}
消息数量: {len(session.messages)}
任务数量: {len(session.tasks)}
        """

        self.console.print(Panel(info_text, title="会话信息", border_style="blue"))

    async def cleanup(self):
        """Cleanup resources"""
        await self.model_client.close()
